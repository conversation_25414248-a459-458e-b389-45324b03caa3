import { Plugin } from 'vite';
import {
  checkCss,
  run as compile,
} from '../../scripts/compile-and-merge-css.js';
import { run as remove } from '../../scripts/remove-antd-less.js';

export function AntdPkgLessToCss(): Plugin {
  return {
    name: 'antd-pkg-less-to-css',
    apply: 'build',
    writeBundle() {
      if (!process.env.plat) {
        return;
      }
      console.log('xxxxxxx', checkCss);
      if (checkCss) {
        return;
      }
      return compile()
        .then(() => {
          remove();
        })
        .finally(() => {
          return;
        });
    },
  };
}
