// 此插件是为了在构建的时候，检测代码中是否有使用 useAssets 方法，如果有则收集需要的远程静态资源地址，且去远程服务器上检测地址是否有效
import { Plugin } from 'vite';
import { ASSETS_SERVICE } from '../assetsServer';
import fetch from 'isomorphic-fetch';
import * as chalk from 'chalk';

const PATTERN = /useAssets\('(.+?)'\)/g;
const assets = [];

let isWatchMode = false;
export function assetsToUrlPlugin(deviceType: string): Plugin {
  return {
    name: 'assets-to-url',
    enforce: 'pre',
    apply: 'build',
    config: (config, { command }) => {
      if (command === 'build' && config.build && config.build.watch) {
        isWatchMode = true;
      }
    },
    transform(this, code) {
      if (isWatchMode) {
        return code;
      }
      const pattern = /useAssets\('(.+?)'\)/g;
      let result: any[];

      while ((result = pattern.exec(code)) !== null) {
        assets.push(result[1]);
      }
      // 返回原始代码，不做修改
      return code;
    },
    async buildEnd() {
      if (isWatchMode) {
        return null;
      }
      console.log(chalk.green('\n' + '开始检测静态资源地址是否有效'));
      const pathArr = [...new Set(assets)];
      const urlArr = pathArr
        .map((path) => generateUrl(deviceType, path))
        .flat();
      console.log(
        chalk.green('\n' + '共收集到' + urlArr.length + '个静态资源地址'),
      );
      await healthCheck(urlArr);
      console.log(chalk.green('\n' + '静态资源地址检测完毕'));
      return null;
    },
  };
}

const generateUrl = (deviceType: string, path: string) => {
  return Object.values(ASSETS_SERVICE).map(
    (pre) => pre + deviceType + '/' + path,
  );
};

const fetchUrl = (url: string) => {
  return fetch(url).then((res: { status: number }) => {
    if (res.status === 200) {
      return;
    }
    console.log(chalk.red(`静态资源地址 ${url} 无效`));
    return Promise.reject(`静态资源地址 ${url} 无效`);
  });
};

const healthCheck = async (arr: string[]) => {
  return Promise.all(arr.map((url) => fetchUrl(url)));
};
