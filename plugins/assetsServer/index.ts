import { Plugin } from 'vite';
import { defineConstCore } from 'vite-plugin-global-const';

export const ASSETS_SERVICE = {
  'mihoyo.com': 'https://info-static.mihoyo.com/images/iam/assets/',
  'hoyoverse.com': 'https://static-public.emp.hoyoverse.com/images/iam/assets/',
  'hoyoverse-inc.com':
    'https://static-public.hoyoverse-inc.com/images/iam/assets/',
};

export function assetsService(): Plugin {
  return {
    name: 'assets-service',
    enforce: 'pre',
    apply: 'build',
    config: (config) => {
      const ASSETS_SERVICE_CONST = defineConstCore({
        ASSETS_SERVICE_CONST: ASSETS_SERVICE,
      });
      return {
        define: {
          ...config.define,
          ...ASSETS_SERVICE_CONST,
        },
      };
    },
  };
}
