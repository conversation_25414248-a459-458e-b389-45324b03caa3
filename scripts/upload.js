const path = require('path');
const fs = require('fs');
const { exec } = require('child_process');

const localesRoot = path.resolve(
  __dirname,
  '../',
  'packages',
  'shared',
  'src',
  'locales',
);

const getPath = (locale, type = 'ts') => {
  return path.resolve(localesRoot, `${locale}.${type}`);
};

const outDir = path.resolve(__dirname, '../', 'locales');

const getLocalesMaps = () => {
  const dirs = fs.readdirSync(localesRoot);
  return dirs.filter((e) => e !== 'index.ts').map((e) => e.replace('.ts', ''));
};

const createJS = (path) => {
  return new Promise((resolve, reject) => {
    exec(
      `tsc ${path} --outDir ${outDir} --skipLibCheck --target es5`,
      (err) => {
        if (err) {
          reject(`tsc ${path} error: ${err}`);
        }
        const fileName = path
          .replace(localesRoot, outDir)
          .replace('.ts', '.js');
        resolve(fileName);
      },
    );
  });
};

const getContent = (path) => {
  return require(path).default;
};

const createOutDir = () => {
  return new Promise((resolve, reject) => {
    if (!fs.existsSync(outDir)) {
      fs.mkdir(outDir, {}, () => {
        return resolve();
      });
    }
    resolve();
  });
};

const createJson = (content, path) => {
  return new Promise((resolve, reject) => {
    fs.writeFile(path, content, { encoding: 'utf8' }, (err) => {
      if (err) {
        reject(`${path} createJson failed: ${err}`);
      }
      resolve();
    });
  });
};

const deleteJS = (fileName) => {
  return new Promise((resolve, reject) => {
    fs.unlink(fileName, (err) => {
      if (err) {
        reject(`deleteJS ${fileName} failed: ${err}`);
      }
      resolve();
    });
  });
};

const run = async () => {
  console.log('loading ...');
  await createOutDir();
  try {
    const localeMaps = getLocalesMaps();
    for (let i = 0; i < localeMaps.length; i++) {
      const path = getPath(localeMaps[i], 'ts');
      const fileName = await createJS(path);
      const content = getContent(fileName);
      const jsonPath = fileName.replace('.js', '.json');
      await createJson(JSON.stringify(content), jsonPath);
      await deleteJS(fileName);
    }
  } catch (error) {
    console.log(error);
  }
};

run().then(() => {
  console.log('done');
});
