const { globSync } = require('glob');
const path = require('path');
const fs = require('fs');
const less = require('less');
const cssnano = require('cssnano');
const NpmImportPlugin = require('less-plugin-npm-import');
const {
  LessPluginRemoveAntdGlobalStyles,
} = require('less-plugin-remove-antd-global-styles');
const postcss = require('postcss');
const genAntsStyleJS = (root) => {
  const files = globSync('**/*.js', { cwd: root });
  const regx = /import\s+['"]antd\/lib\/(.*?)\/style['"];/g;
  const jsList = files.map((file) => {
    const content = fs.readFileSync(path.resolve(root, file), 'utf8');
    let match;
    let result = [];
    while ((match = regx.exec(content)) !== null) {
      result.push(`antd/lib/${match[1]}/style/index.js`);
    }
    return result;
  });
  return [...new Set(jsList.flat())];
};

const getAntsStyleLess = (files, lessFiles = []) => {
  const lessRegex = /require\(['"](.+?\.less)['"]\);?/g;
  const jsRegex = /require\(['"]((?:.+?\/style)(?!.+\.less))['"]\);?/g;
  let jsFiles = [];
  const originalPath = path.resolve(__dirname, '../packages/pc/node_modules/');
  files.forEach((file) => {
    const content = fs.readFileSync(path.resolve(originalPath, file), 'utf8');
    let match;
    while ((match = lessRegex.exec(content)) !== null) {
      lessFiles.push(
        path.resolve(path.dirname(path.resolve(originalPath, file)), match[1]),
      );
    }
    while ((match = jsRegex.exec(content)) !== null) {
      const absolutePath = match[1].replace('../../', 'antd/lib/');
      jsFiles.push(absolutePath + '/index.js');
    }
    if (jsFiles.length > 0) {
      getAntsStyleLess(jsFiles, lessFiles);
    }
  });
  return [...new Set(lessFiles)];
};

const generateCss = async (lessFiles) => {
  const lessOptions = {
    plugins: [new NpmImportPlugin(), new LessPluginRemoveAntdGlobalStyles()],
    javascriptEnabled: true,
    modifyVars: {
      'ant-prefix': 'iam-design',
      'ant-message': 'iam-design-message',
    },
  };
  let compiledCSS = '';
  for (const file of lessFiles) {
    const content = fs.readFileSync(file, 'utf8');
    const output = await less.render(content, {
      ...lessOptions,
      filename: file,
    });
    compiledCSS += output.css;
  }
  return compiledCSS;
};

const compressionCss = async (css) => {
  const result = await postcss([cssnano()]).process(css, { from: undefined });
  return result.css;
};

const writeCss = (css) => {
  const distPath = path.resolve(__dirname, '../packages/pc/dist-css/esm');
  fs.writeFileSync(path.resolve(distPath, 'index.css'), css);
};

const run = async () => {
  const root = path.resolve(__dirname, '../packages/pc/dist-css/esm');
  const jsFiles = genAntsStyleJS(root);
  const lessFiles = getAntsStyleLess(jsFiles);
  const antdCss = await generateCss(lessFiles);
  const appCss = fs.readFileSync(
    path.resolve(__dirname, '../packages/pc/dist-css/esm/style.css'),
    'utf8',
  );
  const finalCSS = await compressionCss(antdCss + '\n' + appCss);
  writeCss(finalCSS);
};

module.exports = {
  run,
};
