const prompts = require('prompts');
const chalk = require('chalk');
const { exec } = require('child_process');

const getCmd = (tag, registry) => {
  return `lerna publish from-git --dist-tag ${tag}  --registry ${registry} --yes`;
};
const log = console.log;
const questions = [
  {
    type: 'text',
    name: 'distTag',
    message: '请输入 dist-tag',
    initial: 'latest',
  },
  {
    type: 'multiselect',
    name: 'registrys',
    message: '请选择需要发布的 registry',
    choices: [
      {
        title: 'https://info-npm-core.mihoyo.com/',
        value: 'https://info-npm-core.mihoyo.com/',
        selected: true,
      },
      {
        title: 'https://plat-registry-npm.mihoyo.com/',
        value: 'https://plat-registry-npm.mihoyo.com/',
      },
    ],
    min: 1,
    warn: 'it is required',
  },
];

const run = async () => {
  log(chalk.blue('iam publish'));
  const response = await prompts(questions);
  const { distTag, registrys } = response;
  const len = registrys.length;
  for (let i = 0; i < registrys.length; i++) {
    const cmdStr = getCmd(distTag, registrys[i]);
    const process = exec(cmdStr);
    process.stdout.on('data', function (data) {
      log(chalk.green(data + ''));
    });

    process.stderr.on('data', function (data) {
      log(chalk.blue(data + ''));
    });

    process.on('close', (code) => {
      log(`exited with code ${code}`);
    });
  }
};

module.exports = run;
