const path = require('path');
const fs = require('fs');

const cssSourcePath = {
  pc: '@iam/login-pc/dist-css/esm/index.css',
  mb: '@iam/login-mb/dist/esm/style.css',
};

const run = () => {
  const pkg = process.env.pkg;
  const sourcePath = path.resolve(__dirname, `../packages/vue-${pkg}/dist/`);
  const cjsPath = path.resolve(sourcePath, 'cjs/index.js');
  const esmPath = path.resolve(sourcePath, 'esm/index.js');
  addCssDependence(cjsPath, pkg, false);
  addCssDependence(esmPath, pkg, true);
};

const addCssDependence = (filePath, pkg, isEsm) => {
  let content = fs.readFileSync(filePath, 'utf-8');
  const importRegex = /import\s+.*?from\s+['"][^'"]+['"];?/g;
  const requireRegex = /require\(['"][^'"]+['"]\);?/g;
  let lastMatchIndex = -1;
  let match;
  const regex = isEsm ? importRegex : requireRegex;

  while ((match = regex.exec(content)) !== null) {
    lastMatchIndex = match.index + match[0].length;
  }

  if (lastMatchIndex !== -1) {
    const insertionText = isEsm
      ? `\nimport '${cssSourcePath[pkg]}';\n`
      : `\nrequire('${cssSourcePath[pkg]}');\n`;
    content = insertAt(content, lastMatchIndex, insertionText);
  } else {
    content =
      (isEsm
        ? `import '${cssSourcePath[pkg]}';\n`
        : `require('${cssSourcePath[pkg]}');\n`) + content;
  }

  fs.writeFileSync(filePath, content);
};

const insertAt = (original, index, insertion) =>
  `${original.slice(0, index)}${insertion}${original.slice(index)}`;

run();
