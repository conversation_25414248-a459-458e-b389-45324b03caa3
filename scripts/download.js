const axios = require('axios');
const path = require('path');
const fs = require('fs');
const { exec } = require('child_process');

const CUCKOO_API =
  'https://south-gate.mihoyo.com/cuckoo/api/v1/translation/resource';
const PROJECT_NO = 'b7ab623a-10c5-46aa-9c20-eadab07a3710';
const ENV = 'test';

const targetDir = path.resolve(
  __dirname,
  '../',
  'packages',
  'shared',
  'src',
  'locales',
);

const instance = axios.create({
  timeout: 3000,
});
const getData = (res) => {
  const { data } = res;
  return data;
};

instance.interceptors.response.use(getData);

const getRemoteLocales = async () => {
  const params = {
    env: ENV,
    projectNo: PROJECT_NO,
  };
  return instance.post(CUCKOO_API, params);
};

const getRemoteLocaleEntry = async (locales) => {
  const len = locales.length;
  for (let i = 0; i < len; i++) {
    const fileName = getFileName(locales[i].lang);
    const content = await getContent(locales[i].link);
    await writeFile(fileName, content);
  }
};

const getFileName = (lang) => {
  const langMap = {
    'zh-CN': 'zh',
    'en-US': 'en',
  };
  return langMap[lang];
};

const getContent = async (link) => {
  return instance.get(link, {
    responseType: 'json',
  });
};

const writeFile = (fileName, content) => {
  const targetPath = path.resolve(targetDir, `${fileName}.ts`);
  const _content = `
    export default 
      ${JSON.stringify(content)}
    
  `;
  return new Promise((resolve, reject) => {
    fs.writeFile(targetPath, _content, (err) => {
      if (err) {
        reject(`writeFile fileName err: ${err}`);
      }
      resolve();
    });
  });
};

const format = () => {
  return new Promise((resolve, reject) => {
    exec(`yarn format -w "${targetDir}"`, (err) => {
      if (err) {
        reject(`format error: ${err}`);
      }
      resolve();
    });
  });
};

const run = async () => {
  console.log('please loading ...');
  const locales = await getRemoteLocales();
  await getRemoteLocaleEntry(locales.data);
  await format();
};

run().then(() => {
  console.log('done');
});
