const spawn = require('cross-spawn');
const chalk = require('chalk');
const prompts = require('prompts');
const path = require('path');
const semver = require('semver');

const lernaJsonPath = path.resolve(process.cwd(), 'lerna.json');
const { version } = require(lernaJsonPath);

const log = console.log;

const PATCH = 'patch';
const MINOR = 'minor';
const MAJOR = 'major';
const PRE_RELEASE = 'prerelease';

const choices = [
  { title: PRE_RELEASE, value: PRE_RELEASE },
  { title: PATCH, value: PATCH },
  { title: MINOR, value: MINOR },
  { title: MAJOR, value: MAJOR },
];

const question = [
  {
    type: 'select',
    name: 'pkgSemver',
    message: `version can be, current is ${version}`,
    hint: '(Use arrow keys)',
    choices: choices,
    initial: 0,
  },
  {
    type: (prev) => (prev === PRE_RELEASE ? 'text' : null),
    name: 'options',
    message: `请输入 ${PRE_RELEASE} name`,
    initial: 'beta',
  },
];

const run = async () => {
  log(chalk.blue('iam version'));
  return new Promise(async (resolve, reject) => {
    const response = await prompts(question, {
      onCancel() {
        reject();
      },
    });
    const { pkgSemver, options } = response;
    const current = semver.inc(version, pkgSemver, options, '1');
    log(chalk.yellow(`new version is ${current}`));
    const child = spawn(
      'lerna ',
      ['version', current, '--yes', '--force-publish'],
      {
        stdio: 'inherit',
      },
    );
    child.once('close', (code) => {
      if (code !== 0) {
        reject();
      } else {
        resolve();
      }
    });

    child.once('error', reject);
  });
};

module.exports = run;
