const fs = require('fs');
const path = require('path');
const { globSync } = require('glob');
const less = require('less');
const NpmImportPlugin = require('less-plugin-npm-import');
const postcss = require('postcss');
const cssnano = require('cssnano');
const {
  LessPluginRemoveAntdGlobalStyles,
} = require('less-plugin-remove-antd-global-styles');
const chalk = require('chalk');

// antd 样式引入的路径模式
const antdStyleImportRegex = /import\s+['"]antd\/lib\/(.*?)\/style['"];/g;
const modulePath = path.resolve(__dirname, '../packages/pc/node_modules');
const oPath = path.resolve(__dirname, '../packages/pc/dist/esm');
const targetPath = path.resolve(oPath, 'index.css');
// Less 编译选项
const lessOptions = {
  plugins: [new NpmImportPlugin(), new LessPluginRemoveAntdGlobalStyles()],
  javascriptEnabled: true,
  modifyVars: {
    'ant-prefix': 'iam-design',
    'ant-message': 'iam-design-message',
  },
};

// 读取 dist 目录下所有的 *.js 文件
const run = async () => {
  const files = globSync('**/*.js', { cwd: oPath });
  let compiledCSS = '';
  const defaultAntdLess = fs.readFileSync(
    `${modulePath}/antd/lib/style/index.less`,
    'utf8',
  );
  const popoverAntdLess = fs.readFileSync(
    `${modulePath}/antd/lib/popover/style/index.less`,
    'utf8',
  );
  const defaultOutput = await less.render(defaultAntdLess, {
    ...lessOptions,
    paths: [`${modulePath}/antd/lib/style`],
  });
  const popoverOutput = await less.render(popoverAntdLess, {
    ...lessOptions,
    paths: [`${modulePath}/antd/lib/popover/style`],
  });
  compiledCSS += defaultOutput.css + '\n' + popoverOutput.css;
  for (const file of files) {
    const content = fs.readFileSync(path.resolve(oPath, file), 'utf8');

    // 查找并处理每个引用的 antd 样式
    let match;
    while ((match = antdStyleImportRegex.exec(content)) !== null) {
      const antdStylePath = `${modulePath}/antd/lib/${match[1]}/style/index.less`;
      try {
        const antdLessContent = fs.readFileSync(antdStylePath, 'utf8');
        const output = await less.render(antdLessContent, {
          ...lessOptions,
          paths: [`${antdStylePath.replace('/index.less', '')}`],
        });
        compiledCSS += output.css;
      } catch (compileError) {
        console.error(`Error compiling ${antdStylePath}:`, compileError);
      }
    }
  }
  // 将编译后的 CSS 合并到 style.css 中
  const styleCSSPath = path.join(oPath, 'style.css');
  const styleCSSContent = fs.readFileSync(styleCSSPath, 'utf8');
  // 压缩并清除重复
  const finalCSS = await cleanCss(compiledCSS + '\n' + styleCSSContent);
  // 写入最终的 index.css 文件
  fs.writeFileSync(path.resolve(oPath, 'index.css'), finalCSS);
};

const cleanCss = async (css) => {
  try {
    console.log(chalk.green('Processing and minimizing index.css...'));
    const result = await postcss([cssnano]).process(css, { from: undefined });
    console.log(chalk.green('CSS processed and minimized successfully!'));
    return result.css;
  } catch (error) {
    console.error(
      chalk.red('Error processing and minimizing index.css:'),
      error,
    );
  }
};

const checkCss = fs.existsSync(targetPath);

run();
