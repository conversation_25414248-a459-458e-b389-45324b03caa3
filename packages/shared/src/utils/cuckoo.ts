import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Env, <PERSON>, localeKey } from '@shared/types';
import request, { cuckooRequest } from '@shared/utils/request';
import { CUCKOO_API } from '@shared/utils/map';

const getRemoteLocales = async (
  env: Env,
  lang: Lang,
  projectNo = 'b7ab623a-10c5-46aa-9c20-eadab07a3710',
) => {
  const finalEnv = env === 'pp' ? 'prod' : env;
  const params = {
    env: finalEnv,
    projectNo,
  };
  const data = await request<CuckooRes>(CUCKOO_API, params, {
    withCredentials: false,
  });

  if (data.code === 0) {
    const locales = await getRemoteLocaleEntry(
      data?.data.filter((e) => e.lang === lang)?.[0],
    );
    return {
      [`${lang}`]: locales,
    } as unknown as { [key in Lang]: localeKey };
  }
  return {
    [`${lang}`]: {},
  } as unknown as { [key in Lang]: localeKey };
};

const getRemoteLocaleEntry = (data: Cuckoo) => {
  const link = data?.link;
  return cuckooRequest<localeKey>(link);
};

export { getRemoteLocales };
