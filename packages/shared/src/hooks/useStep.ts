import { useMemo } from 'react';
import { useCounter } from 'ahooks';
import useLocale from '@shared/hooks/useLocale';
import {
  setRegisterName,
  setRegisterPassword,
  verifyRegisterEmail,
  verifyRegisterPhone,
} from '@shared/routes';
import { useConfig } from '@shared/hooks/useConfig';
import {
  getEncryptedClientId,
  getKeyToRegister,
  rsa,
} from '@shared/utils/utils';
import request from '@shared/utils/request';
import type { Response } from '@shared/types';
import useGetRegisterChain from '@shared/hooks/useGetRegisterChain';
import { MESSAGE } from '@shared/utils/initMessage';
import { REG_WORK_CODE } from '@shared/utils/map';

const MAX_STEP = 5;
const MIN_STEP = 1;

const registerStepDescArr = [
  {
    current: 1,
    api: setRegisterName,
  },
  {
    current: 2,
    api: verifyRegisterEmail,
  },
  {
    current: 3,
    api: verifyRegister<PERSON>hone,
  },
  {
    current: 4,
    api: setRegisterPassword,
  },
];

const stepReplaceFromLocale = (searchValue: string, replaceValue: string) => {
  return `${searchValue.replace('%s', replaceValue)}`;
};

const useStep = () => {
  const { clientId, sourceRedirectUrl, personalCenterApi, env } = useConfig();
  const { registerSessionId } = useGetRegisterChain();
  const __ = useLocale();
  const [current, { inc, dec, set }] = useCounter(MIN_STEP, {
    min: MIN_STEP,
    max: MAX_STEP,
  });
  const stepText = useMemo(() => {
    return `${
      stepReplaceFromLocale(__('register:step:current'), current.toString()) +
      stepReplaceFromLocale(
        __('register:step:total'),
        (MAX_STEP - 1).toString(),
      )
    }`;
  }, [current, __]);

  const rsaPassword = async (password: string) => {
    const authKey = await getKeyToRegister(personalCenterApi, clientId);
    const { publicKey, hash } = authKey;
    return rsa(publicKey, password + hash);
  };
  const submit = async (val: any) => {
    if (current === 5) {
      if (sourceRedirectUrl) {
        window.location.href = sourceRedirectUrl;
        return true;
      }
    }
    const { api } = registerStepDescArr[current - 1];
    let params: {};
    if (current === 4) {
      const { password, secondPassword } = val;
      if (password !== secondPassword) {
        MESSAGE.error(__('pwd:verify'));
        return;
      }
      const _password = await rsaPassword(password);
      const publicKey = await getEncryptedClientId(env, registerSessionId);
      if (!publicKey) {
        return;
      }
      const _clientId =
        new URL(location.href).searchParams.get('clientId') ?? clientId;
      const c = rsa(publicKey, _clientId);
      params = {
        password: _password,
        c,
      };
    } else if (current === 3) {
      params = {
        ...val,
        areaCode: val.areaCode?.value,
      };
    } else {
      params = {
        ...val,
      };
    }
    const data = await request<Response>(`${personalCenterApi}/${api}`, {
      ...params,
      registerSessionId,
    });
    if (data?.code === REG_WORK_CODE) {
      MESSAGE.error(data?.message);
      window.location.reload();
    }
    return data.code === 0;
  };

  const isShowLoginBtn = useMemo(() => {
    return !!sourceRedirectUrl;
  }, [sourceRedirectUrl]);
  return {
    stepText,
    current,
    skip: inc,
    inc,
    max: MAX_STEP,
    min: MIN_STEP,
    submit,
    isShowLoginBtn,
    registerSessionId,
  };
};

export default useStep;
