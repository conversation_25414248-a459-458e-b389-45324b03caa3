import { useConfig } from '@shared/hooks/useConfig';
import { useRequest } from 'ahooks';
import type { GetRegistryChainRes } from '@shared/types';
import request from '@shared/utils/request';
import { getRegistryChain } from '@shared/routes';

const useGetRegisterChain = () => {
  const { personalCenterApi } = useConfig();
  const { data, loading } = useRequest<GetRegistryChainRes, any>(
    async () =>
      await request<GetRegistryChainRes>(
        `${personalCenterApi}/${getRegistryChain}`,
        {},
        {},
      ),
    {
      cacheKey: 'registry-chain',
      staleTime: -1,
    },
  );
  return {
    registerSessionId: data?.data?.registerSessionId || '',
    loading,
  };
};

export default useGetRegisterChain;
