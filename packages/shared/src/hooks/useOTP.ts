import { useConfig } from '@shared/hooks/useConfig';
import { useBoolean, useRequest } from 'ahooks';
import type {
  LdapStatusRes,
  OTPCode,
  OTPCodeListRes,
  Response,
} from '@shared/types';
import request from '@shared/utils/request';
import {
  personalAccountLockStatus,
  personalAccountUnlock,
  personalBindPublicOTP,
  personalOTPList,
  personalUnBindPublicOTP,
} from '@shared/routes';
import { useState } from 'react';
import { OTP_PASSWD_PUBLIC_KEY } from '@shared/utils/map';
import { rsa } from '@shared/utils/utils';
import URLSafeBase64 from 'urlsafe-base64';

const DEFAULT_POLLING_INTERVAL = -1;

const useOTP = () => {
  const { personalCenterApi, env } = useConfig();
  const [pollingInterval, setPollingInterval] = useState<number>(
    DEFAULT_POLLING_INTERVAL,
  );
  const [myAccount, setMyAccount] = useState<OTPCode>(
    null as unknown as OTPCode,
  );
  const [otpList, setOtpList] = useState<OTPCode[]>([] as unknown as OTPCode[]);
  const [visible, { toggle }] = useBoolean(true);

  const { runAsync: getOtpList, cancel } = useRequest<OTPCodeListRes, any>(
    async () =>
      request<OTPCodeListRes>(`${personalCenterApi}/${personalOTPList}`, {}),
    {
      pollingInterval: pollingInterval,
      pollingErrorRetryCount: 1,
      // 为了解决 pollingInterval 依赖于 getOtpList 的响应， 如果直接使用
      // useRequest 的 data, 会有闭包问题
      onSuccess: (data) => {
        if (data?.code === 0) {
          // const myAccount = data?.data?.find((item) => item?.priority === 1);
          const _otpList = data?.data
            ?.sort((a) => {
              if (a.priority === 1) {
                return -1;
              }
              return 1;
            })
            .map((item) => item);
          const [myAccount, ...rest] = _otpList;
          setPollingInterval(Number(myAccount?.overTime) * 1000);
          setMyAccount(myAccount);
          setOtpList(rest);
        } else {
          cancel();
        }
      },
      onError: () => {
        cancel();
      },
    },
  );

  const { data: ldapStatusRes, runAsync: getLdapStatus } = useRequest<
    LdapStatusRes,
    any
  >(
    async () =>
      request<LdapStatusRes>(
        `${personalCenterApi}/${personalAccountLockStatus}`,
        {},
      ),
    {
      onSuccess: (data) => {
        return data;
      },
    },
  );

  const unLock = async (domain: string) => {
    if (!domain) return;
    const data = await request<Response>(
      `${personalCenterApi}/${personalAccountUnlock}`,
      { domain },
    );
    if (data.code === 0) {
      await Promise.all([getOtpList(), getLdapStatus()]);
      return true;
    }
    return false;
  };

  const bindAccount = async (val: { userName: string; password: string }) => {
    const { password, userName } = val;
    const _password = URLSafeBase64.encode(
      rsa(OTP_PASSWD_PUBLIC_KEY?.[env || 'prod'], password),
    );
    const params = {
      userName,
      password: _password,
    };
    const data = await request<Response>(
      `${personalCenterApi}/${personalBindPublicOTP}`,
      { ...params },
    );
    if (data?.code === 0) {
      await Promise.all([getOtpList(), getLdapStatus()]);
      return true;
    }
    return false;
  };

  const unBind = async (name: string) => {
    if (!name) return;
    const params = {
      userNames: [name],
    };
    const data = await request<Response>(
      `${personalCenterApi}/${personalUnBindPublicOTP}`,
      { ...params },
    );
    if (data?.code === 0) {
      await Promise.all([getOtpList(), getLdapStatus()]);
      return true;
    }
    return false;
  };

  return {
    myAccount,
    otpList,
    isLock: ldapStatusRes?.data?.status === 1,
    visible,
    toggle,
    unLock,
    bindAccount,
    unBind,
  };
};

export default useOTP;
