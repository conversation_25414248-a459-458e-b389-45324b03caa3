import { useRequest } from 'ahooks';
import type { IV3LoginConfigOut } from '@shared/types';
import { authnV3LoginPageConfigApi, request, useConfig } from '@shared/index';

const usePageConfig = ({ manual }: { manual?: boolean } = {}) => {
  const { clientId, authApi } = useConfig();
  const { data, loading, refreshAsync, runAsync } = useRequest<
    IV3LoginConfigOut,
    any
  >(
    () =>
      request<IV3LoginConfigOut>(`${authApi}/${authnV3LoginPageConfigApi}`, {
        clientId,
      }),
    {
      refreshDeps: [clientId],
      cacheKey: 'pageConfig',
      manual,
    },
  );
  return {
    data,
    loading,
    refreshAsync,
    runAsync,
  };
};

export default usePageConfig;
