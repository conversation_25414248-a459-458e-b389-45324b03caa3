import { getPersonalVerifyMethods, personIdentityVerify } from '@shared/routes';
import request from '@shared/utils/request';
import { useConfig } from '@shared/hooks/useConfig';
import type {
  GetPersonalMfaVerifyMethodRes,
  GetPersonalMfaVerifyRes,
  SecondProps,
} from '@shared/types';
import { useSetState } from 'ahooks';

const useUserMfa = () => {
  const { personalCenterApi } = useConfig();
  const [props, setProps] = useSetState<SecondProps>(null);

  const getUserVerifyMethods = async () => {
    const data = await request<GetPersonalMfaVerifyMethodRes>(
      `${personalCenterApi}/${getPersonalVerifyMethods}`,
      {},
    );
    if (data?.code === 0) {
      return data?.data?.['2FAConfigs'];
    } else {
      return [];
    }
  };

  const getVerifyProps = async (cb: () => void) => {
    const method = await getUserVerifyMethods();
    setProps({
      visible: true,
      method,
      closable: true,
      disableClose: true,
      handleVisible: (visible: boolean) => {
        setProps({ visible });
      },
      cb,
    });
  };
  const verify = async (cb: () => void) => {
    const data = await request<GetPersonalMfaVerifyRes>(
      `${personalCenterApi}/${personIdentityVerify}`,
      {},
    );
    if (data.code === 0) {
      if (!data?.data) {
        await getVerifyProps(cb);
      } else {
        cb();
      }
    }
  };

  return {
    verify,
    props,
  };
};

export default useUserMfa;
