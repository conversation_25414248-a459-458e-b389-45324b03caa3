import type { Env, Lang } from '@shared/types';
import { useState } from 'react';
import { useAsyncEffect } from 'ahooks';
import {
  getIsWaveEnv,
  getLanguageFromUA,
  getRemoteLocale,
} from '@shared/utils/utils';

const useRemoteLang = (env: Env, defaultLang: Lang) => {
  const [_lang, _setLang] = useState<Lang | undefined>('zh-CN');
  // 设置当前语言
  useAsyncEffect(async () => {
    if (getIsWaveEnv()) {
      const lang = getLanguageFromUA(navigator?.userAgent);
      _setLang(lang);
    } else {
      const remoteLang = await getRemoteLocale(env);
      _setLang(defaultLang || remoteLang);
    }
  }, [defaultLang]);
  return {
    remoteLang: _lang,
  };
};

export default useRemoteLang;
