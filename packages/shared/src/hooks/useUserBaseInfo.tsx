import { useAsyncEffect, useRequest } from 'ahooks';
import request from '@shared/utils/request';
import type {
  LdapStatusRes,
  UserBaseInfo,
  UserBaseInfoRes,
  UserEmailInfo,
  UserPhoneInfo,
} from '@shared/types';
import {
  AccountType,
  Response,
  UserEmailType,
  UserPhoneNumberTag,
} from '@shared/types';
import { useConfig } from '@shared/hooks/useConfig';
import {
  getUserBaseInfo,
  personalAccountLockStatus,
  personalBindEmail,
  personalBindPhone,
  personalChangePhone,
  personalEditEmail,
  personalUnBindPhone,
  setUserName,
} from '@shared/routes';
import { createContext, FC, useContext, useMemo, useState } from 'react';
import dayjs from 'dayjs';

const PHONE_LIMIT = 10;

interface BaseInfoContextProps extends UserBaseInfo {
  isCanAddPhone: boolean;
  isCanAddEmail: boolean;
  loading: boolean;
  getIsAllowUnBindPhone: (phone: UserPhoneInfo) => boolean;
  getIsAllowEditEmail: (email: UserEmailInfo) => boolean;
  isShowExtraName: boolean;
  isShowEditIcon: boolean;
  setName: (name: string) => Promise<boolean>;
  bindPhone: (val: any) => Promise<boolean>;
  editPhone: (val: any) => Promise<boolean>;
  unBindPhone: (val: any) => Promise<boolean>;
  bindEmail: (val: any) => Promise<boolean>;
  editEmail: (val: any) => Promise<boolean>;
  isShowOTPList: boolean;
  isShowMidPasswd: boolean;
  isShowLdapPasswd: boolean;
  isShowSecondPasswd: boolean;
  passwordExpireDate: string;
}

const BaseInfoContext = createContext<BaseInfoContextProps>(
  null as unknown as BaseInfoContextProps,
);

const BaseInfoProvider: FC = ({ children }) => {
  const { env, personalCenterApi } = useConfig();
  const [passwordExpireDate, setPasswordExpireDate] = useState<string>('');
  const { data, loading, runAsync } = useRequest<UserBaseInfoRes, any>(
    async () =>
      await request<UserBaseInfoRes>(
        `${personalCenterApi}/${getUserBaseInfo}`,
        {},
      ),
  );

  const userInfo = useMemo(() => {
    if (data?.code === 0) {
      return data?.data;
    }
    return {} as unknown as UserBaseInfo;
  }, [data]);
  const {
    emailOutList = [],
    phoneNumberList = [],
    accountType,
    domain,
    tapdEmail,
    ...rest
  } = userInfo;
  const isCanAddPhone = phoneNumberList.length < PHONE_LIMIT;
  const getIsAllowUnBindPhone = (phone: UserPhoneInfo) => {
    if (phoneNumberList.length > 1) {
      return phone.phoneNumberTag !== UserPhoneNumberTag['PRIMARY_PHONE'];
    }
    return false;
  };
  const isCanAddEmail = useMemo(() => {
    return !emailOutList.some(
      (e) => e?.emailType === UserEmailType.PERSONAL_EMAIL,
    );
  }, [accountType, emailOutList]);

  const isShowEditIcon = accountType === AccountType.PERSONAL_ACCOUNT;
  const getIsAllowEditEmail = (email: UserEmailInfo) => {
    if (accountType === AccountType.PERSONAL_ACCOUNT) {
      return true;
    }
    return email.emailType === UserEmailType.PERSONAL_EMAIL;
  };
  const isShowExtraName = accountType === AccountType.EMP_ACCOUNT;
  const setName = async (name: string) => {
    if (!name) {
      return false;
    }
    const data = await request<Response>(
      `${personalCenterApi}/${setUserName}`,
      { name },
    );

    if (data?.code === 0) {
      await runAsync();
      return true;
    }
    return false;
  };

  const bindPhone = async (phoneInfo: any) => {
    const { areaCode, ...rest } = phoneInfo;
    const data = await request<Response>(
      `${personalCenterApi}/${personalBindPhone}`,
      {
        ...rest,
        areaCode: areaCode?.value,
      },
    );
    if (data?.code === 0) {
      await runAsync();
      return true;
    }
    return false;
  };

  const editPhone = async (phoneInfo: any) => {
    const { areaCode, ...rest } = phoneInfo;
    const data = await request<Response>(
      `${personalCenterApi}/${personalChangePhone}`,
      {
        ...rest,
        areaCode: areaCode?.value,
      },
    );
    if (data?.code === 0) {
      await runAsync();
      return true;
    }
    return false;
  };

  const unBindPhone = async (phoneInfo: any) => {
    const data = await request<Response>(
      `${personalCenterApi}/${personalUnBindPhone}`,
      { ...phoneInfo },
    );
    if (data?.code === 0) {
      await runAsync();
      return true;
    }
    return false;
  };

  const bindEmail = async (emailInfo: any) => {
    const data = await request<Response>(
      `${personalCenterApi}/${personalBindEmail}`,
      { ...emailInfo },
    );
    if (data?.code === 0) {
      await runAsync();
      return true;
    }
    return false;
  };

  const editEmail = async (emailInfo: any) => {
    const data = await request<Response>(
      `${personalCenterApi}/${personalEditEmail}`,
      { ...emailInfo },
    );
    if (data?.code === 0) {
      await runAsync();
      return true;
    }
    return false;
  };

  const isShowOTPList = accountType === AccountType.EMP_ACCOUNT && !!domain;

  const isShowMidPasswd = accountType === AccountType.PERSONAL_ACCOUNT;

  const isShowLdapPasswd = accountType === AccountType.EMP_ACCOUNT;

  const isSHowSecondPasswd = accountType === AccountType.EMP_ACCOUNT;

  const isShowSecondPasswd = accountType === AccountType.EMP_ACCOUNT;

  const { data: ldapStatusRes, runAsync: getLdapStatus } = useRequest<
    LdapStatusRes,
    any
  >(
    async () =>
      request<LdapStatusRes>(
        `${personalCenterApi}/${personalAccountLockStatus}`,
        {},
      ),
    {
      manual: true,
    },
  );

  useAsyncEffect(async () => {
    if (accountType === AccountType.EMP_ACCOUNT) {
      await getLdapStatus().then((res) => {
        if (res?.code === 0) {
          const passwordExpireTime = res?.data?.passwordExpireTime;
          const passwordExpireDate = dayjs(Number(passwordExpireTime)).format(
            'YYYY-MM-DD HH:mm:ss',
          );
          setPasswordExpireDate(passwordExpireDate);
        }
      });
    }
  }, [accountType]);

  return (
    <BaseInfoContext.Provider
      value={{
        emailOutList,
        phoneNumberList,
        isCanAddPhone,
        isCanAddEmail,
        accountType,
        loading,
        getIsAllowUnBindPhone,
        getIsAllowEditEmail,
        isShowExtraName,
        isShowEditIcon,
        setName,
        bindPhone,
        editPhone,
        unBindPhone,
        bindEmail,
        editEmail,
        isShowOTPList,
        isShowMidPasswd,
        isShowLdapPasswd,
        isShowSecondPasswd,
        passwordExpireDate,
        tapdEmail,
        ...rest,
      }}
    >
      {children}
    </BaseInfoContext.Provider>
  );
};

export default BaseInfoProvider;

const useBaseInfoContext = () => {
  return useContext(BaseInfoContext);
};

export const useBaseInfo = () => {
  return useBaseInfoContext();
};
