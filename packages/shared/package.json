{"name": "@iam/login-shared", "version": "4.0.0-thirdApp.230", "description": "info-iam-login-shared", "main": "dist/cjs/index.js", "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "author": "chen.qian", "license": "MIT", "files": ["dist", "CHANGELOG.md"], "scripts": {"build": "vite build && tsc && tsc-alias", "dev": "pnpm run /^dev:.*/", "dev:vite": "vite build --watch", "dev:tsc": "tsc -w", "dev:tsc-alias": "tsc-alias -w"}, "devDependencies": {"@cuckoo/locale-shared": "^0.0.1-alpha.0", "@originjs/vite-plugin-commonjs": "^1.0.3", "@sentry/types": "^7.117.0", "@types/crypto-js": "^4.2.1", "@types/js-cookie": "^3.0.2", "@types/react": "17.0.2", "@types/react-dom": "17.0.2"}, "dependencies": {"@otakus/design": "^0.7.8", "@babel/runtime": "^7.22.5", "@hoyowave/jsapi": "0.1.5-beta.1", "@hoyowave/login": "0.0.7", "@iam/message-channel": "^0.2.0", "@magic-microservices/magic": "^1.1.3", "@plat/mihoyo-device-fingerprint-jssdk": "^0.3.10", "ahooks": "^3.7.6", "axios": "^1.6.8", "dayjs": "^1.11.9", "dexie": "^3.2.4", "js-cookie": "^3.0.1", "jsencrypt": "^3.3.2", "query-string": "^7.1.1", "react": "17.0.2", "react-dom": "17.0.2", "rxjs": "^7.5.5", "ua-parser-js": "^1.0.33", "urlsafe-base64": "^1.0.0", "uuid": "^8.3.2"}}