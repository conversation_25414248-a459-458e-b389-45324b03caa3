# @iam/login-ui

## 4.0.0-thirdApp.230

### Patch Changes

- 支持 ZT ticket 获取
- Updated dependencies
  - @iam/login-shared@4.0.0-thirdApp.230

## 4.0.0-thirdApp.229

### Patch Changes

- feat: 在登录组件中添加第三方认证支持
- Updated dependencies
  - @iam/login-shared@4.0.0-thirdApp.229

## 4.0.0-thirdApp.228

### Patch Changes

- fix: update zt-ticket param to zt_ticket in goTripartiteAuth function
- Updated dependencies
  - @iam/login-shared@4.0.0-thirdApp.228

## 4.0.0-thirdApp.227

### Patch Changes

- fix: zt 不阻断任何流程
- Updated dependencies
  - @iam/login-shared@4.0.0-thirdApp.227

## 4.0.0-thirdApp.226

### Patch Changes

- feat: 增加 ZT 票据异常处理和错误提示
- Updated dependencies
  - @iam/login-shared@4.0.0-thirdApp.226

## 4.0.0-thirdApp.225

### Patch Changes

- refactor: 临时注释 refreshZtTokenInWave 调用
- Updated dependencies
  - @iam/login-shared@4.0.0-thirdApp.225

## 4.0.0-thirdApp.224

### Patch Changes

- 三方认证流程，增加 ZT 票据支持
- Updated dependencies
  - @iam/login-shared@4.0.0-thirdApp.224

## 4.0.0-hw.224

### Patch Changes

- fix: 添加 otp_token 支持到 useFlowToken hook
- Updated dependencies
  - @iam/login-shared@4.0.0-hw.224

## 4.0.0-hw.223

### Patch Changes

- feat: 优化登录流程和错误处理机制
- Updated dependencies
  - @iam/login-shared@4.0.0-hw.223

## 4.0.0-hw.222

### Patch Changes

- Revert "refactor: extract login form hooks into separate files"
- Updated dependencies
  - @iam/login-shared@4.0.0-hw.222

## 4.0.0-hw.221

### Patch Changes

- fix: style
- Updated dependencies
  - @iam/login-shared@4.0.0-hw.221

## 4.0.0-hw.220

### Patch Changes

- refactor: update vite config for better compatibility and debugging
- Updated dependencies
  - @iam/login-shared@4.0.0-hw.220

## 4.0.0-hw.219

### Patch Changes

- refactor: extract login form hooks into separate files
- Updated dependencies
  - @iam/login-shared@4.0.0-hw.219

## 4.0.0-hw.218

### Patch Changes

- style: adjust button font sizes and simplify otp border styles
- Updated dependencies
  - @iam/login-shared@4.0.0-hw.218

## 4.0.0-hw.217

### Patch Changes

- style: adjust logout button styles for consistency
- Updated dependencies
  - @iam/login-shared@4.0.0-hw.217

## 4.0.0-hw.216

### Patch Changes

- style: adjust otp and login form styles for mobile responsiveness
- Updated dependencies
  - @iam/login-shared@4.0.0-hw.216

## 4.0.0-hw.215

### Patch Changes

- fix: adjust modal width and clean up otp link url
- Updated dependencies
  - @iam/login-shared@4.0.0-hw.215

## 4.0.0-hw.214

### Patch Changes

- feat: privacy policy translations
- Updated dependencies
  - @iam/login-shared@4.0.0-hw.214

## 4.0.0-hw.213

### Patch Changes

- chore: update @otakus/design version and remove duplicate StyleProvider
- Updated dependencies
  - @iam/login-shared@4.0.0-hw.213

## 4.0.0-hw.212

### Patch Changes

- feat: update otp bind and verify ui with video modal and help links
- 37e4af4: feat: add luming animation support
- c5630e1: feat: add luming animation support
- Updated dependencies
- Updated dependencies [37e4af4]
- Updated dependencies [c5630e1]
  - @iam/login-shared@4.0.0-hw.212

## 4.0.0-hw.211

### Patch Changes

- │ fix: handle undefined data in LoginForm policy check
- Updated dependencies
  - @iam/login-shared@4.0.0-hw.211

## 4.0.0-hw.210

### Patch Changes

- feat: add sentry error reporting to login components
- Updated dependencies
  - @iam/login-shared@4.0.0-hw.210

## 4.0.0-hw.209

### Major Changes

- │ feat: add hardware binding SDK with crypto and storage support

### Patch Changes

- Updated dependencies
  - @iam/login-shared@4.0.0-hw.209

## 4.0.0-exchangeApproval.208

### Patch Changes

- feat: 更新多语言
- Updated dependencies
  - @iam/login-shared@4.0.0-exchangeApproval.208

## 4.0.0-exchangeApproval.207

### Patch Changes

- feat: 换绑表单 modal
- Updated dependencies
  - @iam/login-shared@4.0.0-exchangeApproval.207

## 4.0.0-exchangeApproval.206

### Patch Changes

- feat: 更新多语言
- Updated dependencies
  - @iam/login-shared@4.0.0-exchangeApproval.206

## 4.0.0-exchangeApproval.205

### Patch Changes

- fix: otp title
- Updated dependencies
  - @iam/login-shared@4.0.0-exchangeApproval.205

## 4.0.0-exchangeApproval.204

### Patch Changes

- feat: 更新多语言
- Updated dependencies
  - @iam/login-shared@4.0.0-exchangeApproval.204

## 4.0.0-exchangeApproval.203

### Patch Changes

- feat: 更新多语言
- Updated dependencies
  - @iam/login-shared@4.0.0-exchangeApproval.203

## 4.0.0-exchangeApproval.202

### Patch Changes

- feat: 多语言更新
- Updated dependencies
  - @iam/login-shared@4.0.0-exchangeApproval.202

## 4.0.0-exchangeApproval.201

### Patch Changes

- fix: 测试逻辑
- Updated dependencies
  - @iam/login-shared@4.0.0-exchangeApproval.201

## 4.0.0-exchangeApproval.200

### Patch Changes

- feat:250423
- Updated dependencies
  - @iam/login-shared@4.0.0-exchangeApproval.200

## 4.0.0-exChangeOtp.199

### Patch Changes

- fix: style
- Updated dependencies
  - @iam/login-shared@4.0.0-exChangeOtp.199

## 4.0.0-exChangeOtp.198

### Patch Changes

- fix: style
- Updated dependencies
  - @iam/login-shared@4.0.0-exChangeOtp.198

## 4.0.0-exChangeOtp.197

### Patch Changes

- fix: style
- Updated dependencies
  - @iam/login-shared@4.0.0-exChangeOtp.197

## 4.0.0-exChangeOtp.196

### Patch Changes

- fixL style
- Updated dependencies
  - @iam/login-shared@4.0.0-exChangeOtp.196

## 4.0.0-exChangeOtp.195

### Patch Changes

- fix: style
- Updated dependencies
  - @iam/login-shared@4.0.0-exChangeOtp.195

## 4.0.0-exChangeOtp.194

### Patch Changes

- fix: locale bugs
- Updated dependencies
  - @iam/login-shared@4.0.0-exChangeOtp.194

## 4.0.0-exChangeOtp.193

### Patch Changes

- feat: 更新多语言
- Updated dependencies
  - @iam/login-shared@4.0.0-exChangeOtp.193

## 4.0.0-exChangeOtp.192

### Patch Changes

- fix: style
- Updated dependencies
  - @iam/login-shared@4.0.0-exChangeOtp.192

## 4.0.0-exChangeOtp.191

### Patch Changes

- upd: login@4.0.0-exChangeOtp.191
- Updated dependencies
  - @iam/login-shared@4.0.0-exChangeOtp.191

## 4.0.0-exChangeOtp.190

### Patch Changes

- feat: 单独使用 mfa 可关闭弹窗
- Updated dependencies
  - @iam/login-shared@4.0.0-exChangeOtp.190

## 4.0.0-exChangeOtp.189

### Patch Changes

- upd: login@@4.0.0-exChangeOtp.188
- Updated dependencies
  - @iam/login-shared@4.0.0-exChangeOtp.189

## 4.0.0-exChangeOtp.188

### Patch Changes

- fix: 在 wave 端内不唤起快速登录
- Updated dependencies
  - @iam/login-shared@4.0.0-exChangeOtp.188

## 4.0.0-exChangeOtp.187

### Patch Changes

- feat: 文案修改
- Updated dependencies
  - @iam/login-shared@4.0.0-exChangeOtp.187

## 4.0.0-exChangeOtp.186

### Patch Changes

- upd: login@4.0.0-exChangeOtp.186
- Updated dependencies
  - @iam/login-shared@4.0.0-exChangeOtp.186

## 4.0.0-exChangeOtp.185

### Patch Changes

- iam/login-ui
- Updated dependencies
  - @iam/login-shared@4.0.0-exChangeOtp.185

## 4.0.0-exChangeOtp.184

### Patch Changes

- feat: 立即注册
- Updated dependencies
  - @iam/login-shared@4.0.0-exChangeOtp.184

## 4.0.0-exChangeOtp.183

### Patch Changes

- fix: 0326 bug
- Updated dependencies
  - @iam/login-shared@4.0.0-exChangeOtp.183

## 4.0.0-exChangeOtp.182

### Patch Changes

- feat: player
- Updated dependencies
  - @iam/login-shared@4.0.0-exChangeOtp.182

## 4.0.0-exChangeOtp.181

### Patch Changes

- fix: INVALID_TOTP_VERIFY_CODE_V2 清除表单状态
- Updated dependencies
  - @iam/login-shared@4.0.0-exChangeOtp.181

## 4.0.0-exChangeOtp.180

### Patch Changes

- feat: showBackButton
- Updated dependencies
  - @iam/login-shared@4.0.0-exChangeOtp.180

## 4.0.0-exChangeOtp.179

### Patch Changes

- feat: 0326
- Updated dependencies
  - @iam/login-shared@4.0.0-exChangeOtp.179

## 4.0.0-otpText.178

### Patch Changes

- feat: 250326
- Updated dependencies
  - @iam/login-shared@4.0.0-otpText.178

## 4.0.0-otpText.177

### Patch Changes

- feat: 绑定提示
- Updated dependencies
  - @iam/login-shared@4.0.0-otpText.177

## 4.0.0-otpText.176

### Patch Changes

- fix: moatClientId 获取 兼容 HOYOVERSE_DOMAIN
- Updated dependencies
  - @iam/login-shared@4.0.0-otpText.176

## 4.0.0-otpText.175

### Patch Changes

- e732c8e: feat: hoyoverse
- Merge branch 'feat-next-otaku-hoyoverse' into feat-otaku-0219
- Updated dependencies [e732c8e]
- Updated dependencies
  - @iam/login-shared@4.0.0-otpText.175

## 4.0.0-otpText.174

### Patch Changes

- feat: 更改 otp:bind:btn 文案
- Updated dependencies
  - @iam/login-shared@4.0.0-otpText.174

## 4.0.0-mfaClose.173

### Patch Changes

- fix: 标题闪烁
- Updated dependencies
  - @iam/login-shared@4.0.0-mfaClose.173

## 4.0.0-mfaClose.172

### Patch Changes

- d4f60dd: feat: 减少客户端主动取消请求的 sentry 错误上报
- upd: login@4.0.0-mfaClose.172
- Updated dependencies [d4f60dd]
- Updated dependencies
  - @iam/login-shared@4.0.0-mfaClose.172

## 4.0.0-mfaClose.171

### Patch Changes

- upd: login@4.0.0-mfaClose.171
- Updated dependencies
  - @iam/login-shared@4.0.0-mfaClose.171

## 4.0.0-mfaClose.170

### Patch Changes

- feat: 支持 showMfaCloseIcon，以及弹窗关闭时间
- Updated dependencies
  - @iam/login-shared@4.0.0-mfaClose.170

## 4.0.0-otpNext.169

### Patch Changes

- upd: login@4.0.0-otpNext.169
- Updated dependencies
  - @iam/login-shared@4.0.0-otpNext.169

## 4.0.0-otpNext.168

### Patch Changes

- upd: login@4.0.0-otpNext.168
- Updated dependencies
  - @iam/login-shared@4.0.0-otpNext.168

## 4.0.0-otpNext.167

### Patch Changes

- upd: login@4.0.0-otpNext.167
- Updated dependencies
  - @iam/login-shared@4.0.0-otpNext.167

## 4.0.0-otpNext.166

### Patch Changes

- upd: login@4.0.0-otpNext.166
- Updated dependencies
  - @iam/login-shared@4.0.0-otpNext.166

## 4.0.0-otpNext.165

### Patch Changes

- upd: login@4.0.0-otpNext.165
- Updated dependencies
  - @iam/login-shared@4.0.0-otpNext.165

## 4.0.0-otpNext.164

### Patch Changes

- release: 4.0.0-otpNext.164
- Updated dependencies
  - @iam/login-shared@4.0.0-otpNext.164

## 4.0.0-otpNext.163

### Patch Changes

- release: 4.0.0-otpNext.163
- Updated dependencies
  - @iam/login-shared@4.0.0-otpNext.163

## 4.0.0-otpNext.162

### Minor Changes

- release: login@4.1.0-otpNext.161

### Patch Changes

- Updated dependencies
  - @iam/login-shared@4.0.0-otpNext.162

## 4.0.0-otpNext.161

### Patch Changes

- release:4.0.0-otpNext.161
- Updated dependencies
  - @iam/login-shared@4.0.0-otpNext.161

## 4.0.0-otpNext.160

### Patch Changes

- upd: login@4.0.0-otpNext.160
- Updated dependencies
  - @iam/login-shared@4.0.0-otpNext.160

## 4.0.0-otpNext.159

### Patch Changes

- upd: login@4.0.0-otpNext.159
- Updated dependencies
  - @iam/login-shared@4.0.0-otpNext.159

## 4.0.0-otpNext.158

### Patch Changes

- release: login@4.0.0-otpNext.158
- Updated dependencies
  - @iam/login-shared@4.0.0-otpNext.158

## 4.0.0-otpNext.157

### Patch Changes

- release:4.0.0-otpNext.156
- Updated dependencies
  - @iam/login-shared@4.0.0-otpNext.157

## 4.0.0-otpFeature.156

### Patch Changes

- fix: codeInput
- Updated dependencies
  - @iam/login-shared@4.0.0-otpFeature.156

## 4.0.0-otpFeature.155

### Patch Changes

- upd: login@4.0.0-otpFeature.154
- Updated dependencies
  - @iam/login-shared@4.0.0-otpFeature.155

## 4.0.0-otpFeature.154

### Patch Changes

- release:4.0.0-otpFeature.154
- Updated dependencies
  - @iam/login-shared@4.0.0-otpFeature.154

## 4.0.0-otpFeature.153

### Patch Changes

- release: login@4.0.0-otpFeature.153
- Updated dependencies
  - @iam/login-shared@4.0.0-otpFeature.153

## 4.0.0-otpFeature.152

### Patch Changes

- release: 4.0.0-241211.152
- Updated dependencies
  - @iam/login-shared@4.0.0-otpFeature.152

## 4.0.0-241211.151

### Minor Changes

- release: 4.0.0-241211.151

### Patch Changes

- Updated dependencies
  - @iam/login-shared@4.0.0-241211.151

## 4.0.0-241211.150

### Minor Changes

- release: 4.0.0-241211.148

### Patch Changes

- Updated dependencies
  - @iam/login-shared@4.0.0-241211.150

## 4.0.0-vmfa.149

### Patch Changes

- release: 4.0.0-vmfa.148
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.149

## 4.0.0-vmfa.148

### Patch Changes

- release: 4.0.0-vmfa.148
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.148

## 4.0.0-vmfa.147

### Patch Changes

- release: 4.0.0-vmfa.147
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.147

## 4.0.0-vmfa.146

### Patch Changes

- release: 4.0.0-vmfa.146
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.146

## 4.0.0-vmfa.145

### Patch Changes

- release: 4.0.0-vmfa.145
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.145

## 4.0.0-vmfa.144

### Patch Changes

- release: 4.0.0-vmfa.143
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.144

## 4.0.0-vmfa.143

### Patch Changes

- fix: 二验
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.143

## 4.0.0-vmfa.142

### Patch Changes

- release: 4.0.0-vmfa.142
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.142

## 4.0.0-vmfa.141

### Patch Changes

- release: 4.0.0-vmfa.141
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.141

## 4.0.0-vmfa.140

### Patch Changes

- release: 4.0.0-vmfa.40
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.140

## 4.0.0-vmfa.139

### Patch Changes

- release: 4.0.0-vmfa.139
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.139

## 4.0.0-vmfa.138

### Patch Changes

- release: 4.0.0-vmfa.138
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.138

## 4.0.0-vmfa.137

### Patch Changes

- fix: style
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.137

## 4.0.0-vmfa.136

### Patch Changes

- release: 4.0.0-vmfa.136
- 0b38f6c: feat: wave 免登 同步登录态
- Updated dependencies
- Updated dependencies [0b38f6c]
  - @iam/login-shared@4.0.0-vmfa.136

## 4.0.0-vmfa.135

### Patch Changes

- release: 4.0.0-vmfa.135
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.135

## 4.0.0-vmfa.134

### Patch Changes

- release: 4.0.0-vmfa.134
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.134

## 4.0.0-vmfa.133

### Patch Changes

- release: 4.0.0-vmfa.133
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.133

## 4.0.0-vmfa.132

### Patch Changes

- release: 4.0.0-vmfa.132
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.132

## 4.0.0-vmfa.131

### Patch Changes

- release: 4.0.0-vmfa.131
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.131

## 4.0.0-vmfa.130

### Patch Changes

- release: 4.0.0-vmfa.130
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.130

## 4.0.0-vmfa.129

### Patch Changes

- release: 4.0.0-vmfa.129
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.129

## 4.0.0-vmfa.128

### Patch Changes

- release: 4.0.0-vmfa.128
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.128

## 4.0.0-vmfa.127

### Patch Changes

- release: 4.0.0-vmfa.127
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.127

## 4.0.0-vmfa.126

### Patch Changes

- fix: style
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.126

## 4.0.0-vmfa.125

### Patch Changes

- release: 4.0.0-vmfa.125
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.125

## 4.0.0-vmfa.124

### Patch Changes

- release: 4.0.0-vmfa.124
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.124

## 4.0.0-vmfa.123

### Patch Changes

- release: 4.0.0-vmfa.123
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.123

## 4.0.0-vmfa.122

### Patch Changes

- release: 4.0.0-vmfa.122
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.122

## 4.0.0-vmfa.121

### Patch Changes

- release: 4.0.0-vmfa.121
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.121

## 4.0.0-vmfa.120

### Patch Changes

- release: 4.0.0-vmfa.120
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.120

## 4.0.0-vmfa.119

### Patch Changes

- release: login@4.0.0-vmfa.119
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.119

## 4.0.0-vmfa.118

### Patch Changes

- release: login@4.0.0-vmfa.118
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.118

## 4.0.0-vmfa.117

### Patch Changes

- release: login@4.0.0-vmfa.117
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.117

## 4.0.0-vmfa.116

### Patch Changes

- release: login@4.0.0-vmfa.116
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.116

## 4.0.0-vmfa.115

### Patch Changes

- release: 4.0.0-vmfa.114
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.115

## 4.0.0-vmfa.114

### Patch Changes

- fix: some bugs
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.114

## 4.0.0-vmfa.113

### Patch Changes

- release: 4.0.0-otaku.112
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.113

## 4.0.0-vmfa.112

### Patch Changes

- release: 4.0.0-otaku.111
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.112

## 4.0.0-vmfa.111

### Patch Changes

- release: 4.0.0-vmfa.111
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.111

## 4.0.0-vmfa.110

### Patch Changes

- release: 4.0.0-vmfa.110
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.110

## 4.0.0-vmfa.109

### Patch Changes

- release: 4.0.0-vmfa.109
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.109

## 4.0.0-vmfa.108

### Patch Changes

- release: 4.0.0-vmfa.109
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.108

## 4.0.0-vmfa.107

### Patch Changes

- release: vmufa init
- Updated dependencies
  - @iam/login-shared@4.0.0-vmfa.107

## 4.0.0-otaku.111

### Patch Changes

- feat: wave 免登 同步登录态
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.111

## 4.0.0-otaku.110

### Patch Changes

- fix: PhoneInput type
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.110

## 4.0.0-otaku.109

### Patch Changes

- release: 4.0.0-otaku.109
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.109

## 4.0.0-otaku.108

### Patch Changes

- release: 4.0.0-otaku.108
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.108

## 4.0.0-otaku.107

### Patch Changes

- release: 4.0.0-otaku.107
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.107

## 4.0.0-otaku.106

### Patch Changes

- release: 4.0.0-otaku.106
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.106

## 4.0.0-otaku.105

### Patch Changes

- release: 4.0.0-otaku.105
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.105

## 4.0.0-otaku.104

### Patch Changes

- release: 4.0.0-otaku.104
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.104

## 4.0.0-otaku.103

### Patch Changes

- release: 4.0.0-otaku.103
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.103

## 4.0.0-otaku.102

### Patch Changes

- release: 4.0.0-otaku.102
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.102

## 4.0.0-otaku.101

### Patch Changes

- release: 4.0.0-otaku.101
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.101

## 4.0.0-otaku.100

### Patch Changes

- release: 4.0.0-otaku.100
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.100

## 4.0.0-otaku.99

### Patch Changes

- release: 4.0.0-otaku.99
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.99

## 4.0.0-otaku.98

### Patch Changes

- release: 4.0.0-otaku.98
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.98

## 4.0.0-otaku.97

### Patch Changes

- release: 4.0.0-otaku.97
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.97

## 4.0.0-otaku.96

### Patch Changes

- release: 4.0.0-otaku.96
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.96

## 4.0.0-otaku.95

### Patch Changes

- code format
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.95

## 4.0.0-otaku.94

### Patch Changes

- release: 4.0.0-otaku.94
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.94

## 4.0.0-otaku.93

### Patch Changes

- release: 4.0.0-otaku.93
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.93

## 4.0.0-otaku.92

### Patch Changes

- release: 4.0.0-otaku.92
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.92

## 4.0.0-otaku.91

### Patch Changes

- release: 4.0.0-otaku.90
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.91

## 4.0.0-otaku.90

### Patch Changes

- release: 4.0.0-otaku.90
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.90

## 4.0.0-otaku.89

### Patch Changes

- release: 4.0.0-otaku.89
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.89

## 4.0.0-otaku.88

### Patch Changes

- release: 4.0.0-otaku.88
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.88

## 4.0.0-otaku.87

### Patch Changes

- release: 4.0.0-otaku.87
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.87

## 4.0.0-otaku.86

### Patch Changes

- release: 4.0.0-otaku.86
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.86

## 4.0.0-otaku.85

### Patch Changes

- release: 4.0.0-otaku.85
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.85

## 4.0.0-otaku.84

### Patch Changes

- release: 4.0.0-otaku.84
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.84

## 4.0.0-otaku.83

### Patch Changes

- release: 4.0.0-otaku.83
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.83

## 4.0.0-otaku.82

### Patch Changes

- release: 4.0.0-otaku.82
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.82

## 4.0.0-otaku.81

### Patch Changes

- release: 4.0.0-otaku.81
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.81

## 4.0.0-otaku.80

### Patch Changes

- release: 4.0.0-otaku.80
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.80

## 4.0.0-otaku.79

### Patch Changes

- release: 4.0.0-otaku.79
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.79

## 4.0.0-otaku.78

### Patch Changes

- release: v4.0.0-otaku.78
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.78

## 4.0.0-otaku.77

### Patch Changes

- feat: 调整一些样式
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.77

## 4.0.0-otaku.76

### Patch Changes

- feat: sm 尺寸样式
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.76

## 4.0.0-otaku.75

### Patch Changes

- fix: style
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.75

## 4.0.0-otaku.74

### Patch Changes

- fix: style
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.74

## 4.0.0-otaku.73

### Patch Changes

- feat: 样式调整
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.73

## 4.0.0-otaku.72

### Patch Changes

- feat: NextComponentSecondVerification
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.72

## 4.0.0-otaku.71

### Patch Changes

- 7.76 kB │ gzip: 2.52 kB │ map: 4.94 kB
- release: v4.0.0-otaku
- Updated dependencies
- Updated dependencies
  - @iam/login-shared@4.0.0-otaku.71
